<template>
  <div class="app-container home">
    <!-- 欢迎区域 -->
    <el-card class="welcome-card" shadow="never">
      <div class="welcome-content">
        <div class="welcome-text">
          <h1 class="welcome-title">欢迎使用果树认养管理系统</h1>
          <p class="welcome-desc">专业的果树认养业务管理平台，为您提供全方位的管理解决方案</p>
        </div>
        <div class="welcome-icon">
          <i class="el-icon-s-data" style="font-size: 80px; color: #67C23A;"></i>
        </div>
      </div>
    </el-card>

    <!-- 数据统计区域 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :xs="24" :sm="8" :md="8" :lg="8">
        <el-card class="stats-card" shadow="hover">
          <div class="stats-content">
            <div class="stats-icon user-icon">
              <i class="el-icon-user"></i>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ statsData.userCount }}</div>
              <div class="stats-label">用户总数</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="24" :sm="8" :md="8" :lg="8">
        <el-card class="stats-card" shadow="hover">
          <div class="stats-content">
            <div class="stats-icon tree-icon">
              <i class="el-icon-s-opportunity"></i>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ statsData.fruitTreeCount }}</div>
              <div class="stats-label">果树总数</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="24" :sm="8" :md="8" :lg="8">
        <el-card class="stats-card" shadow="hover">
          <div class="stats-content">
            <div class="stats-icon order-icon">
              <i class="el-icon-s-order"></i>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ statsData.orderCount }}</div>
              <div class="stats-label">订单总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { getSystemStats } from '@/api/system/stats'

export default {
  name: "Index",
  data() {
    return {
      // 统计数据
      statsData: {
        userCount: 0,
        fruitTreeCount: 0,
        orderCount: 0
      },
      loading: false
    }
  },
  created() {
    this.getStatsData()
  },
  methods: {
    // 获取统计数据
    async getStatsData() {
      this.loading = true
      try {
        const response = await getSystemStats()
        if (response.code === 200) {
          this.statsData = response.data
        }
      } catch (error) {
        console.error('获取统计数据失败:', error)
        this.$message.error('获取统计数据失败')
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.home {
  padding: 20px;

  .welcome-card {
    margin-bottom: 20px;
    border: none;

    .welcome-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 20px 0;

      .welcome-text {
        flex: 1;

        .welcome-title {
          font-size: 28px;
          color: #303133;
          margin: 0 0 10px 0;
          font-weight: 600;
        }

        .welcome-desc {
          font-size: 16px;
          color: #606266;
          margin: 0;
          line-height: 1.6;
        }
      }

      .welcome-icon {
        margin-left: 20px;
      }
    }
  }

  .stats-row {
    margin-top: 20px;

    .stats-card {
      margin-bottom: 20px;
      transition: all 0.3s;

      &:hover {
        transform: translateY(-2px);
      }

      .stats-content {
        display: flex;
        align-items: center;
        padding: 10px 0;

        .stats-icon {
          width: 60px;
          height: 60px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 20px;

          i {
            font-size: 24px;
            color: white;
          }

          &.user-icon {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          }

          &.tree-icon {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
          }

          &.order-icon {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
          }
        }

        .stats-info {
          flex: 1;

          .stats-number {
            font-size: 32px;
            font-weight: bold;
            color: #303133;
            line-height: 1;
            margin-bottom: 5px;
          }

          .stats-label {
            font-size: 14px;
            color: #909399;
          }
        }
      }
    }
  }
}
</style>
