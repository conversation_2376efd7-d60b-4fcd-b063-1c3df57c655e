<template>
  <div class="app-container">
    <el-form ref="form" :model="form" :rules="rules" label-width="120px">
      <el-card class="box-card">
        <div slot="header" class="clearfix">
          <span>基本信息</span>
        </div>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="果树名称" prop="name">
              <el-input v-model="form.name" placeholder="请输入果树名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="品牌" prop="brand">
              <el-input v-model="form.brand" placeholder="请输入品牌" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="品种" prop="variety">
              <el-input v-model="form.variety" placeholder="请输入品种" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="产品类型" prop="productType">
              <el-input v-model="form.productType" placeholder="请输入产品类型" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="描述" prop="description">
          <el-input v-model="form.description" type="textarea" :rows="3" placeholder="请输入果树描述" />
        </el-form-item>
      </el-card>

      <el-card class="box-card">
        <div slot="header" class="clearfix">
          <span>价格库存</span>
        </div>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="价格" prop="price">
              <el-input-number v-model="form.price" :min="0" :precision="2" placeholder="请输入价格" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="总库存" prop="totalStock">
              <el-input-number v-model="form.totalStock" :min="0" placeholder="请输入总库存" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="已售库存">
              <el-input-number v-model="form.soldStock" :min="0" disabled style="width: 100%" />
              <div style="font-size: 12px; color: #999; margin-top: 4px;">系统自动计算，不可编辑</div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="认养周期" prop="period">
              <el-select v-model="form.period" placeholder="请选择认养周期" style="width: 100%">
                <el-option label="6个月" :value="6" />
                <el-option label="12个月" :value="12" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="运费" prop="freightPrice">
              <el-input-number v-model="form.freightPrice" :min="0" :precision="2" placeholder="请输入运费" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="上架状态" prop="marketEnable">
              <el-switch
                v-model="form.marketEnable"
                active-text="上架"
                inactive-text="下架"
                active-value="UPPER"
                inactive-value="DOWN"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <el-card class="box-card">
        <div slot="header" class="clearfix">
          <span>产品规格</span>
        </div>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="产地" prop="origin">
              <el-input v-model="form.origin" placeholder="请输入产地" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="单果直径" prop="diameter">
              <el-input v-model="form.diameter" placeholder="请输入单果直径" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="规格包装" prop="specPackage">
              <el-input v-model="form.specPackage" placeholder="请输入规格包装" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="存储条件" prop="storageCondition">
              <el-input v-model="form.storageCondition" placeholder="请输入存储条件" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="保质期" prop="shelflife">
              <el-input v-model="form.shelflife" placeholder="请输入保质期" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="日照时间" prop="sunshineHours">
              <el-input-number v-model="form.sunshineHours" :min="0" :max="24" placeholder="请输入日照时间" style="width: 100%" />
              <div style="font-size: 12px; color: #999; margin-top: 4px;">单位：小时/天</div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="发货时间" prop="shippingTime">
          <el-input v-model="form.shippingTime" placeholder="请输入发货时间，如：收获后3-5天发货" style="width: 400px" />
        </el-form-item>
      </el-card>

      <el-card class="box-card">
        <div slot="header" class="clearfix">
          <span>认养权益</span>
        </div>

        <el-form-item label="权益说明" prop="benefits">
          <el-input v-model="form.benefits" type="textarea" :rows="4" placeholder="请输入认养权益，每行一个权益" />
        </el-form-item>

        <el-form-item label="节省费用" prop="reduceCosts">
          <el-input v-model="form.reduceCosts" placeholder="请输入节省费用说明，如：相比市场价节省300元" />
        </el-form-item>
      </el-card>

      <el-card class="box-card">
        <div slot="header" class="clearfix">
          <span>图片信息</span>
        </div>

        <el-form-item label="主图片">
          <el-upload
            class="avatar-uploader"
            :action="uploadUrl"
            :headers="uploadHeaders"
            :show-file-list="false"
            :on-success="handleImageSuccess"
            :before-upload="beforeImageUpload"
            accept="image/*"
          >
            <img v-if="form.image" :src="form.image" class="avatar">
            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
          </el-upload>
        </el-form-item>

        <el-form-item label="轮播图片">
          <el-upload
            class="upload-demo"
            :action="uploadUrl"
            :headers="uploadHeaders"
            :on-success="handleBannerSuccess"
            :on-remove="handleBannerRemove"
            :file-list="bannerFileList"
            list-type="picture-card"
            accept="image/*"
          >
            <i class="el-icon-plus"></i>
          </el-upload>
        </el-form-item>
      </el-card>

      <el-card class="box-card">
        <div slot="header" class="clearfix">
          <span>图文详情</span>
        </div>
        <el-form-item label-width="0" prop="fruitDetail">
          <el-upload
            :action="uploadUrl"
            :headers="uploadHeaders"
            :on-success="handleDetailImageSuccess"
            :file-list="detailImageList"
            :on-remove="handleDetailImageRemove"
            list-type="picture-card"
            accept="image/*"
          >
            <i slot="default" class="el-icon-plus"></i>
            <div slot="file" slot-scope="{file}">
              <img
                class="el-upload-list__item-thumbnail"
                :src="file.url"
                alt=""
              >
              <span class="el-upload-list__item-actions">
                <span
                  class="el-upload-list__item-preview"
                  @click="moveImage(findFileIndex(file), 'up')"
                  :class="{'disabled': findFileIndex(file) === 0}"
                >
                  <i class="el-icon-top"></i>
                </span>
                <span
                  class="el-upload-list__item-preview"
                  @click="moveImage(findFileIndex(file), 'down')"
                  :class="{'disabled': findFileIndex(file) === detailImageList.length - 1}"
                >
                  <i class="el-icon-bottom"></i>
                </span>
                <span
                  class="el-upload-list__item-delete"
                  @click="handleDetailImageRemove(file)"
                >
                  <i class="el-icon-delete"></i>
                </span>
              </span>
            </div>
          </el-upload>
          <div style="font-size: 12px; color: #999; margin-top: 4px;">点击上传图片，悬浮可调整顺序</div>
        </el-form-item>
      </el-card>

      <div style="text-align: center; margin-top: 20px;">
        <el-button @click="cancel">取消</el-button>
        <el-button type="primary" @click="submitForm">保存</el-button>
      </div>
    </el-form>
  </div>
</template>

<script>
import { getFruitTree, addFruitTree, updateFruitTree } from '@/api/fruit/tree'
import { getToken } from '@/utils/auth'

export default {
  name: 'FruitTreeEdit',
  components: {},
  data() {
    return {
      form: {
        id: null,
        name: '',
        description: '',
        image: '',
        price: 0,
        totalStock: 0,
        soldStock: 0,
        brand: '',
        productType: '',
        diameter: '',
        shelflife: '',
        origin: '',
        specPackage: '',
        storageCondition: '',
        variety: '',
        benefits: '',
        reduceCosts: '',
        period: 6,
        bannerImages: '',
        freightPrice: 0,
        marketEnable: 'DOWN',
        sunshineHours: 0,
        shippingTime: '',
        fruitDetail: ''
      },
      rules: {
        name: [
          { required: true, message: '果树名称不能为空', trigger: 'blur' }
        ],
        brand: [
          { required: true, message: '品牌不能为空', trigger: 'blur' }
        ],
        variety: [
          { required: true, message: '品种不能为空', trigger: 'blur' }
        ],
        price: [
          { required: true, message: '价格不能为空', trigger: 'blur' }
        ],
        totalStock: [
          { required: true, message: '总库存不能为空', trigger: 'blur' }
        ],
        period: [
          { required: true, message: '认养周期不能为空', trigger: 'change' }
        ]
      },
      bannerFileList: [],
      detailImageList: [],
      uploadUrl: process.env.VUE_APP_BASE_API + '/common/upload',
      uploadHeaders: {
        Authorization: 'Bearer ' + getToken()
      }
    }
  },
  created() {
    const id = this.$route.query.id
    if (id) {
      this.loadData(id)
    }
  },
  methods: {
    loadData(id) {
      getFruitTree(id).then(response => {
        this.form = { ...this.form, ...response.data }

        // 处理轮播图片
        if (this.form.bannerImages) {
          this.bannerFileList = this.form.bannerImages.split(',').map((url, index) => ({
            name: `banner-${index}`,
            url: url.trim()
          }))
        }
        // 处理图文详情图片
        if (this.form.fruitDetail) {
          this.detailImageList = this.form.fruitDetail.split(',').map((url, index) => ({
            name: `detail-${index}`,
            url: url.trim()
          }))
        }
      }).catch(() => {
        this.$message.error('获取数据失败')
      })
    },
    handleImageSuccess(response) {
      if (response.code === 200) {
        this.form.image = response.url || response.data?.url
      } else {
        this.$message.error('上传失败')
      }
    },
    handleBannerSuccess(response, file, fileList) {
      if (response.code === 200) {
        const url = response.url || response.data?.url
        this.bannerFileList = fileList.map(item => ({
          name: item.name,
          url: item.response ? (item.response.url || item.response.data?.url) : item.url
        }))
        this.updateBannerImages()
      } else {
        this.$message.error('上传失败')
      }
    },
    handleBannerRemove(file, fileList) {
      this.bannerFileList = fileList
      this.updateBannerImages()
    },
    updateBannerImages() {
      this.form.bannerImages = this.bannerFileList.map(item => item.url).join(',')
    },
    beforeImageUpload(file) {
      const isImage = file.type.indexOf('image/') === 0
      const isLt2M = file.size / 1024 / 1024 < 2

      if (!isImage) {
        this.$message.error('只能上传图片文件!')
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 2MB!')
      }
      return isImage && isLt2M
    },
    handleDetailImageSuccess(response, file, fileList) {
      if (response.code === 200) {
        const url = response.url || response.data?.url
        // el-upload的fileList是自动管理的，我们只需要处理好自己的数据源
        const newFile = fileList[fileList.length - 1]
        this.detailImageList.push({
          name: newFile.name,
          url: response.url || response.data?.url,
          uid: newFile.uid
        })
        this.updateDetailImages()
      } else {
        this.$message.error('上传失败')
      }
    },
    handleDetailImageRemove(file) {
      const index = this.findFileIndex(file)
      if (index !== -1) {
        this.detailImageList.splice(index, 1)
        this.updateDetailImages()
      }
    },
    updateDetailImages() {
      this.form.fruitDetail = this.detailImageList.map(item => item.url).join(',')
    },
    moveImage(index, direction) {
      if (index === -1) return
      if (direction === 'up' && index > 0) {
        const temp = this.detailImageList[index]
        this.detailImageList.splice(index, 1)
        this.detailImageList.splice(index - 1, 0, temp)
      } else if (direction === 'down' && index < this.detailImageList.length - 1) {
        const temp = this.detailImageList[index]
        this.detailImageList.splice(index, 1)
        this.detailImageList.splice(index + 1, 0, temp)
      }
      this.updateDetailImages()
    },
    findFileIndex(file) {
      return this.detailImageList.findIndex(item => item.uid === file.uid)
    },
    submitForm() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.updateDetailImages() // 提交前确保图文详情已更新
          const submitData = { ...this.form }
          // 编辑时不修改已售库存，保持原值
          // 已售库存由系统根据订单自动计算

          if (submitData.id) {
            updateFruitTree(submitData).then(() => {
              this.$modal.msgSuccess("修改成功")
              // 通知列表页面刷新
              this.$eventBus.$emit('fruitTreeListRefresh')
              this.cancel()
            }).catch(() => {
              this.$modal.msgError("修改失败")
            })
          } else {
            // 新增时已售库存固定为0
            submitData.soldStock = 0
            addFruitTree(submitData).then(() => {
              this.$modal.msgSuccess("新增成功")
              // 通知列表页面刷新
              this.$eventBus.$emit('fruitTreeListRefresh')
              this.cancel()
            }).catch(() => {
              this.$modal.msgError("新增失败")
            })
          }
        }
      })
    },
    cancel() {
      this.$router.push('/tree/list')
    }
  }
}
</script>

<style scoped>
.box-card {
  margin-bottom: 20px;
}

.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 148px;
  height: 148px;
  line-height: 148px;
  text-align: center;
}

.avatar {
  width: 148px;
  height: 148px;
  display: block;
}

.el-upload-list__item-actions .el-upload-list__item-preview,
.el-upload-list__item-actions .el-upload-list__item-delete {
  cursor: pointer;
  margin: 0 5px;
}

.el-upload-list__item-actions .disabled {
  cursor: not-allowed;
  opacity: 0.5;
}
</style>
