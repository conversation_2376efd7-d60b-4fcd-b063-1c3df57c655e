---
title: 果树认养
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 果树认养

Base URLs:

# Authentication

- HTTP Authentication, scheme: bearer

# 管理后台

## POST 账号密码-登录接口

POST /auth/login

> Body 请求参数

```json
{
  "username": "admin",
  "password": "admin123"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|object| 否 |none|

> 返回示例

> 200 Response

```json
{
  "msg": "string",
  "code": 0,
  "expiresTime": 0,
  "token": "string"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» msg|string|true|none||none|
|» code|integer|true|none||none|
|» expiresTime|integer|true|none||none|
|» token|string|true|none||none|

## POST 上传文件

POST /common/upload

> Body 请求参数

```yaml
file: file://C:\Users\<USER>\Pictures\微信图片_20250711192234.png

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|object| 否 |none|
|» file|body|string(binary)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "msg": "string",
  "code": 0
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» msg|string|true|none||none|
|» code|integer|true|none||none|

## POST 新增果树

POST /system/fruitTree/add

> Body 请求参数

```json
{
  "name": "富士苹果树-2025年度认养",
  "description": "认养专属富士苹果树，体验从开花到结果的完整生长周期。每棵树保证产量不低于50斤，可远程观看生长直播，收获季免费配送上门。",
  "image": "https://example.com/images/fuji_apple_tree.jpg",
  "price": 1999,
  "totalStock": 100,
  "soldStock": 23,
  "brand": "绿野果园",
  "productType": "果树认养",
  "diameter": "5-8cm（树干直径）",
  "shelflife": "冷藏保存30天",
  "origin": "山东烟台核心产区",
  "specPackage": "10kg/箱（预计单树产量）",
  "storageCondition": "0-4℃冷藏",
  "variety": "红富士",
  "benefits": "专属树牌+生长监测+免费采摘体验+定制礼盒包装",
  "period": 365,
  "bannerImages": "https://example.com/banners/fuji1.jpg,https://example.com/banners/fuji2.jpg"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|object| 否 |none|

> 返回示例

> 200 Response

```json
{
  "msg": "string",
  "code": 0
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» msg|string|true|none||none|
|» code|integer|true|none||none|

## GET 果树列表

GET /system/fruitTree/list

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|pageNum|query|string| 否 |none|
|pageSize|query|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "total": 0,
  "rows": [
    {
      "createBy": null,
      "createTime": null,
      "updateBy": null,
      "updateTime": null,
      "remark": null,
      "variety": "string",
      "storageCondition": "string",
      "specPackage": "string",
      "origin": "string",
      "shelflife": "string",
      "diameter": "string",
      "productType": "string",
      "brand": "string",
      "soldStock": 0,
      "totalStock": 0,
      "price": 0,
      "image": "string",
      "description": "string",
      "name": "string",
      "benefits": "string",
      "period": 0,
      "bannerImages": "string",
      "id": 0
    }
  ],
  "code": 0,
  "msg": "string"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» total|integer|true|none||none|
|» rows|[object]|true|none||none|
|»» createBy|null|false|none||none|
|»» createTime|null|false|none||none|
|»» updateBy|null|false|none||none|
|»» updateTime|null|false|none||none|
|»» remark|null|false|none||none|
|»» variety|string|false|none||none|
|»» storageCondition|string|false|none||none|
|»» specPackage|string|false|none||none|
|»» origin|string|false|none||none|
|»» shelflife|string|false|none||none|
|»» diameter|string|false|none||none|
|»» productType|string|false|none||none|
|»» brand|string|false|none||none|
|»» soldStock|integer|false|none||none|
|»» totalStock|integer|false|none||none|
|»» price|number|false|none||none|
|»» image|string|false|none||none|
|»» description|string|false|none||none|
|»» name|string|false|none||none|
|»» benefits|string|false|none||none|
|»» period|integer|false|none||none|
|»» bannerImages|string|false|none||none|
|»» id|integer|false|none||none|
|» code|integer|true|none||none|
|» msg|string|true|none||none|

## GET 果树详情

GET /system/fruitTree/1

> 返回示例

> 200 Response

```json
{
  "msg": "string",
  "code": 0,
  "data": {
    "createBy": null,
    "createTime": null,
    "updateBy": null,
    "updateTime": "string",
    "remark": null,
    "variety": "string",
    "storageCondition": "string",
    "specPackage": "string",
    "origin": "string",
    "shelflife": "string",
    "diameter": "string",
    "productType": "string",
    "brand": "string",
    "soldStock": 0,
    "totalStock": 0,
    "price": 0,
    "image": "string",
    "description": "string",
    "name": "string",
    "benefits": "string",
    "period": 0,
    "bannerImages": "string",
    "id": 0,
    "marketEnable": "string",
    "freightPrice": 0
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» msg|string|true|none||none|
|» code|integer|true|none||none|
|» data|object|true|none||none|
|»» createBy|null|true|none||none|
|»» createTime|null|true|none||none|
|»» updateBy|null|true|none||none|
|»» updateTime|string|true|none||none|
|»» remark|null|true|none||none|
|»» variety|string|true|none||none|
|»» storageCondition|string|true|none||none|
|»» specPackage|string|true|none||none|
|»» origin|string|true|none||none|
|»» shelflife|string|true|none||none|
|»» diameter|string|true|none||none|
|»» productType|string|true|none||none|
|»» brand|string|true|none||none|
|»» soldStock|integer|true|none||none|
|»» totalStock|integer|true|none||none|
|»» price|integer|true|none||none|
|»» image|string|true|none||none|
|»» description|string|true|none||none|
|»» name|string|true|none||none|
|»» benefits|string|true|none||none|
|»» period|integer|true|none||none|
|»» bannerImages|string|true|none||none|
|»» id|integer|true|none||none|
|»» marketEnable|string|true|none||none|
|»» freightPrice|integer|true|none||none|

## PUT 更新果树

PUT /system/fruitTree/update

> Body 请求参数

```json
{
  "id": 2,
  "name": "富士苹果树-2025年度认养",
  "description": "认养专属富士苹果树，体验从开花到结果的完整生长周期。每棵树保证产量不低于50斤，可远程观看生长直播，收获季免费配送上门。",
  "image": "https://example.com/images/fuji_apple_tree.jpg",
  "price": 1999,
  "totalStock": 100,
  "soldStock": 25,
  "brand": "绿野果园",
  "productType": "果树认养",
  "diameter": "5-8cm（树干直径）",
  "shelflife": "冷藏保存30天",
  "origin": "山东烟台核心产区",
  "specPackage": "10kg/箱（预计单树产量）",
  "storageCondition": "0-4℃冷藏",
  "variety": "红富士",
  "benefits": "专属树牌+生长监测+免费采摘体验+定制礼盒包装",
  "period": 365,
  "bannerImages": "https://example.com/banners/fuji1.jpg,https://example.com/banners/fuji2.jpg"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|object| 否 |none|

> 返回示例

> 200 Response

```json
{
  "msg": "string",
  "code": 0
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» msg|string|true|none||none|
|» code|integer|true|none||none|

## DELETE 删除果树

DELETE /system/fruitTree/3

> 返回示例

> 200 Response

```json
{
  "total": 0,
  "rows": [
    {
      "createBy": null,
      "createTime": null,
      "updateBy": null,
      "updateTime": null,
      "remark": null,
      "variety": "string",
      "storageCondition": "string",
      "specPackage": "string",
      "origin": "string",
      "shelflife": "string",
      "diameter": "string",
      "productType": "string",
      "brand": "string",
      "soldStock": 0,
      "totalStock": 0,
      "price": 0,
      "image": "string",
      "description": "string",
      "name": "string",
      "benefits": "string",
      "period": 0,
      "bannerImages": "string",
      "id": 0
    }
  ],
  "code": 0,
  "msg": "string"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» total|integer|true|none||none|
|» rows|[object]|true|none||none|
|»» createBy|null|false|none||none|
|»» createTime|null|false|none||none|
|»» updateBy|null|false|none||none|
|»» updateTime|null|false|none||none|
|»» remark|null|false|none||none|
|»» variety|string|false|none||none|
|»» storageCondition|string|false|none||none|
|»» specPackage|string|false|none||none|
|»» origin|string|false|none||none|
|»» shelflife|string|false|none||none|
|»» diameter|string|false|none||none|
|»» productType|string|false|none||none|
|»» brand|string|false|none||none|
|»» soldStock|integer|false|none||none|
|»» totalStock|integer|false|none||none|
|»» price|number|false|none||none|
|»» image|string|false|none||none|
|»» description|string|false|none||none|
|»» name|string|false|none||none|
|»» benefits|string|false|none||none|
|»» period|integer|false|none||none|
|»» bannerImages|string|false|none||none|
|»» id|integer|false|none||none|
|» code|integer|true|none||none|
|» msg|string|true|none||none|

## PUT 果树上架

PUT /system/fruitTree/up/1

> Body 请求参数

```json
{}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|object| 否 |none|

> 返回示例

> 200 Response

```json
{
  "msg": "string",
  "code": 0
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» msg|string|true|none||none|
|» code|integer|true|none||none|

## PUT 果树下架

PUT /system/fruitTree/under/1

> Body 请求参数

```json
{}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|object| 否 |none|

> 返回示例

> 200 Response

```json
{
  "msg": "string",
  "code": 0
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» msg|string|true|none||none|
|» code|integer|true|none||none|

## GET 订单列表

GET /system/fruitOrder/list

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|pageNum|query|string| 否 |none|
|pageSize|query|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "total": 0,
  "rows": [
    {
      "createBy": null,
      "createTime": "string",
      "updateBy": "string",
      "updateTime": null,
      "remark": null,
      "adoptTime": "string",
      "adoptBy": "string",
      "delFlag": "string",
      "status": 0,
      "payStatus": "string",
      "paymentMethod": "string",
      "paymentTime": null,
      "receivableNo": "string",
      "payOrderNo": "string",
      "addressId": 0,
      "fruitTreeId": 0,
      "fruitTreeName": "string",
      "userId": 0,
      "id": 0,
      "fruitNum": 0,
      "freightPrice": 0,
      "contactName": "string",
      "phone": "string",
      "province": "string",
      "city": "string",
      "district": "string",
      "detailAddress": "string",
      "totalPrice": 0,
      "unitPrice": 0,
      "orderSn": "string",
      "labelName": "string",
      "wishMsg": "string"
    }
  ],
  "code": 0,
  "msg": "string"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» total|integer|true|none||none|
|» rows|[object]|true|none||none|
|»» createBy|null|true|none||none|
|»» createTime|string¦null|true|none||none|
|»» updateBy|string|true|none||none|
|»» updateTime|null|true|none||none|
|»» remark|null|true|none||none|
|»» adoptTime|string|true|none||none|
|»» adoptBy|string|true|none||none|
|»» delFlag|string|true|none||none|
|»» status|integer|true|none||none|
|»» payStatus|string¦null|true|none||none|
|»» paymentMethod|string¦null|true|none||none|
|»» paymentTime|null|true|none||none|
|»» receivableNo|string¦null|true|none||none|
|»» payOrderNo|string¦null|true|none||none|
|»» addressId|integer|true|none||none|
|»» fruitTreeId|integer|true|none||none|
|»» fruitTreeName|string¦null|true|none||none|
|»» userId|integer|true|none||none|
|»» id|integer|true|none||none|
|»» fruitNum|integer¦null|true|none||none|
|»» freightPrice|integer¦null|true|none||none|
|»» contactName|string¦null|true|none||none|
|»» phone|string¦null|true|none||none|
|»» province|string¦null|true|none||none|
|»» city|string¦null|true|none||none|
|»» district|string¦null|true|none||none|
|»» detailAddress|string¦null|true|none||none|
|»» totalPrice|integer¦null|true|none||none|
|»» unitPrice|integer¦null|true|none||none|
|»» orderSn|string¦null|true|none||none|
|»» labelName|string¦null|true|none||none|
|»» wishMsg|string¦null|true|none||none|
|» code|integer|true|none||none|
|» msg|string|true|none||none|

## GET 订单详情

GET /system/fruitOrder/1

> 返回示例

> 200 Response

```json
{
  "msg": "string",
  "code": 0,
  "data": {
    "createBy": null,
    "createTime": null,
    "updateBy": "string",
    "updateTime": null,
    "remark": null,
    "adoptTime": "string",
    "adoptBy": "string",
    "delFlag": "string",
    "status": 0,
    "payStatus": null,
    "paymentMethod": null,
    "paymentTime": null,
    "receivableNo": null,
    "payOrderNo": null,
    "addressId": 0,
    "fruitTreeId": 0,
    "fruitTreeName": null,
    "userId": 0,
    "id": 0,
    "fruitNum": null,
    "freightPrice": null,
    "contactName": null,
    "phone": null,
    "province": null,
    "city": null,
    "district": null,
    "detailAddress": null,
    "totalPrice": null,
    "unitPrice": null,
    "orderSn": null,
    "labelName": null,
    "wishMsg": null
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» msg|string|true|none||none|
|» code|integer|true|none||none|
|» data|object|true|none||none|
|»» createBy|null|true|none||none|
|»» createTime|null|true|none||none|
|»» updateBy|string|true|none||none|
|»» updateTime|null|true|none||none|
|»» remark|null|true|none||none|
|»» adoptTime|string|true|none||none|
|»» adoptBy|string|true|none||none|
|»» delFlag|string|true|none||none|
|»» status|integer|true|none||none|
|»» payStatus|null|true|none||none|
|»» paymentMethod|null|true|none||none|
|»» paymentTime|null|true|none||none|
|»» receivableNo|null|true|none||none|
|»» payOrderNo|null|true|none||none|
|»» addressId|integer|true|none||none|
|»» fruitTreeId|integer|true|none||none|
|»» fruitTreeName|null|true|none||none|
|»» userId|integer|true|none||none|
|»» id|integer|true|none||none|
|»» fruitNum|null|true|none||none|
|»» freightPrice|null|true|none||none|
|»» contactName|null|true|none||none|
|»» phone|null|true|none||none|
|»» province|null|true|none||none|
|»» city|null|true|none||none|
|»» district|null|true|none||none|
|»» detailAddress|null|true|none||none|
|»» totalPrice|null|true|none||none|
|»» unitPrice|null|true|none||none|
|»» orderSn|null|true|none||none|
|»» labelName|null|true|none||none|
|»» wishMsg|null|true|none||none|

## POST 发货

POST /system/fruitOrder/delivery

> Body 请求参数

```json
"{\r\n  \"orderSn\": \"SN1951885453921751040\",  // 订单号\r\n  \"logisticsCode\": \"zhongtong\",        // 物流公司编号，来自数据字典\r\n  \"logisticsNo\": \"zt112121212\",        // 物流单号\r\n  \"logisticsName\": \"中通\"               // 物流公司名称，来自数据字典\r\n}"
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|object| 否 |none|

> 返回示例

> 200 Response

```json
{
  "msg": "string",
  "code": 0
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» msg|string|true|none||none|
|» code|integer|true|none||none|

## GET 物流公司字典

GET /system/dict/data/type/logistics_company

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

## GET 订单权益

GET /system/fruitOrder/benefits/SN1951885453921751040

> 返回示例

> 200 Response

```json
{
  "msg": "string",
  "code": 0,
  "data": {
    "createBy": null,
    "createTime": null,
    "updateBy": "string",
    "updateTime": null,
    "remark": null,
    "adoptTime": "string",
    "adoptBy": "string",
    "delFlag": "string",
    "status": 0,
    "payStatus": null,
    "paymentMethod": null,
    "paymentTime": null,
    "receivableNo": null,
    "payOrderNo": null,
    "addressId": 0,
    "fruitTreeId": 0,
    "fruitTreeName": null,
    "userId": 0,
    "id": 0,
    "fruitNum": null,
    "freightPrice": null,
    "contactName": null,
    "phone": null,
    "province": null,
    "city": null,
    "district": null,
    "detailAddress": null,
    "totalPrice": null,
    "unitPrice": null,
    "orderSn": null,
    "labelName": null,
    "wishMsg": null
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» msg|string|true|none||none|
|» code|integer|true|none||none|
|» data|object|true|none||none|
|»» createBy|null|true|none||none|
|»» createTime|null|true|none||none|
|»» updateBy|string|true|none||none|
|»» updateTime|null|true|none||none|
|»» remark|null|true|none||none|
|»» adoptTime|string|true|none||none|
|»» adoptBy|string|true|none||none|
|»» delFlag|string|true|none||none|
|»» status|integer|true|none||none|
|»» payStatus|null|true|none||none|
|»» paymentMethod|null|true|none||none|
|»» paymentTime|null|true|none||none|
|»» receivableNo|null|true|none||none|
|»» payOrderNo|null|true|none||none|
|»» addressId|integer|true|none||none|
|»» fruitTreeId|integer|true|none||none|
|»» fruitTreeName|null|true|none||none|
|»» userId|integer|true|none||none|
|»» id|integer|true|none||none|
|»» fruitNum|null|true|none||none|
|»» freightPrice|null|true|none||none|
|»» contactName|null|true|none||none|
|»» phone|null|true|none||none|
|»» province|null|true|none||none|
|»» city|null|true|none||none|
|»» district|null|true|none||none|
|»» detailAddress|null|true|none||none|
|»» totalPrice|null|true|none||none|
|»» unitPrice|null|true|none||none|
|»» orderSn|null|true|none||none|
|»» labelName|null|true|none||none|
|»» wishMsg|null|true|none||none|

## GET 订单证书

GET /system/fruitOrder/certificate/SN1951885453921751040

> 返回示例

> 200 Response

```json
{
  "msg": "string",
  "code": 0,
  "data": {
    "createBy": null,
    "createTime": null,
    "updateBy": "string",
    "updateTime": null,
    "remark": null,
    "adoptTime": "string",
    "adoptBy": "string",
    "delFlag": "string",
    "status": 0,
    "payStatus": null,
    "paymentMethod": null,
    "paymentTime": null,
    "receivableNo": null,
    "payOrderNo": null,
    "addressId": 0,
    "fruitTreeId": 0,
    "fruitTreeName": null,
    "userId": 0,
    "id": 0,
    "fruitNum": null,
    "freightPrice": null,
    "contactName": null,
    "phone": null,
    "province": null,
    "city": null,
    "district": null,
    "detailAddress": null,
    "totalPrice": null,
    "unitPrice": null,
    "orderSn": null,
    "labelName": null,
    "wishMsg": null
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» msg|string|true|none||none|
|» code|integer|true|none||none|
|» data|object|true|none||none|
|»» createBy|null|true|none||none|
|»» createTime|null|true|none||none|
|»» updateBy|string|true|none||none|
|»» updateTime|null|true|none||none|
|»» remark|null|true|none||none|
|»» adoptTime|string|true|none||none|
|»» adoptBy|string|true|none||none|
|»» delFlag|string|true|none||none|
|»» status|integer|true|none||none|
|»» payStatus|null|true|none||none|
|»» paymentMethod|null|true|none||none|
|»» paymentTime|null|true|none||none|
|»» receivableNo|null|true|none||none|
|»» payOrderNo|null|true|none||none|
|»» addressId|integer|true|none||none|
|»» fruitTreeId|integer|true|none||none|
|»» fruitTreeName|null|true|none||none|
|»» userId|integer|true|none||none|
|»» id|integer|true|none||none|
|»» fruitNum|null|true|none||none|
|»» freightPrice|null|true|none||none|
|»» contactName|null|true|none||none|
|»» phone|null|true|none||none|
|»» province|null|true|none||none|
|»» city|null|true|none||none|
|»» district|null|true|none||none|
|»» detailAddress|null|true|none||none|
|»» totalPrice|null|true|none||none|
|»» unitPrice|null|true|none||none|
|»» orderSn|null|true|none||none|
|»» labelName|null|true|none||none|
|»» wishMsg|null|true|none||none|

# 数据模型

