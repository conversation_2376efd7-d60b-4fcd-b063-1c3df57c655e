import request from '@/utils/request'

/**
 * @typedef {object} StatsData
 * @property {number} userCount - 用户总数
 * @property {number} fruitTreeCount - 果树总数
 * @property {number} orderCount - 订单总数
 */

/**
 * @typedef {object} StatsResponse
 * @property {string} msg - 响应消息
 * @property {number} code - 响应代码
 * @property {StatsData} data - 统计数据
 */

/**
 * 获取系统统计数据
 * @returns {Promise<StatsResponse>}
 */
export function getSystemStats() {
  return request({
    url: '/system/index',
    method: 'get'
  })
}
